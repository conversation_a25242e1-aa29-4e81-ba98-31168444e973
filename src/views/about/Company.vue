<!-- 公司介绍页面 -->
<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    <!-- 页面头部 -->
    <PageHeader>
      关于囤鼠科技
      <template #subtitle>
        洞见AI未来，共塑智能时代 — 了解我们的故事、使命与驱动力
      </template>
    </PageHeader>

    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute top-20 left-10 w-32 h-32 bg-primary-500/10 rounded-full blur-xl"></div>
      <div class="absolute bottom-20 right-10 w-48 h-48 bg-blue-500/10 rounded-full blur-xl"></div>
      <div class="absolute top-1/2 left-1/4 w-24 h-24 bg-purple-500/10 rounded-full blur-lg"></div>
    </div>

    <div class="relative mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
      <!-- 我们是谁 -->
      <div class="mx-auto max-w-4xl opacity-0 fade-in-up">
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold tracking-tight text-gray-900 mb-6">
            {{ companyStory.title }}
          </h2>
          <div class="w-24 h-1 bg-gradient-to-r from-primary-500 to-blue-500 rounded-full mx-auto"></div>
        </div>

        <div class="bg-white rounded-2xl p-8 lg:p-12 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 relative overflow-hidden group">
          <!-- 卡片背景装饰 -->
          <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary-50 to-blue-50 rounded-full opacity-50 transform translate-x-16 -translate-y-16 group-hover:opacity-70 transition-opacity duration-300"></div>
          <div class="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-purple-50 to-pink-50 rounded-full opacity-50 transform -translate-x-12 translate-y-12 group-hover:opacity-70 transition-opacity duration-300"></div>

          <!-- 左侧装饰线 -->
          <div class="absolute left-0 top-8 bottom-8 w-1 bg-gradient-to-b from-primary-400 via-blue-500 to-purple-600 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

          <div class="relative prose prose-lg prose-gray max-w-none">
            <p class="text-xl leading-relaxed text-gray-700 text-justify group-hover:text-gray-800 transition-colors duration-300">
              {{ companyStory.content }}
            </p>
          </div>

          <!-- 底部装饰线条 -->
          <div class="relative mt-8 w-full h-px bg-gradient-to-r from-transparent via-primary-200 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </div>
      </div>

      <!-- 使命与愿景 -->
      <div class="mx-auto mt-24 max-w-6xl sm:mt-32 relative">
        <!-- 背景装饰 -->
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-primary-100 rounded-full opacity-30"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-100 rounded-full opacity-30"></div>

        <div class="relative text-center mb-16 opacity-0 fade-in-up delay-200">
          <h2 class="text-4xl lg:text-5xl font-bold tracking-tight text-gray-900 mb-6">
            使命与
            <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">愿景</span>
          </h2>
          <div class="w-24 h-1 bg-gradient-to-r from-primary-500 to-blue-500 rounded-full mx-auto"></div>
        </div>

        <div class="relative grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- 使命 -->
          <div class="opacity-0 fade-in-up delay-300">
            <div class="bg-white rounded-2xl p-8 lg:p-10 shadow-lg border border-gray-100 h-full hover:shadow-xl transition-all duration-300 relative overflow-hidden group">
              <!-- 卡片装饰 -->
              <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-primary-100 to-blue-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform translate-x-10 -translate-y-10"></div>

              <div class="relative flex items-center mb-6">
                <div class="w-12 h-12 bg-gradient-to-br from-primary-100 to-primary-200 rounded-xl flex items-center justify-center mr-4 group-hover:from-primary-200 group-hover:to-primary-300 transition-all duration-300">
                  <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900">{{ missionVision.mission.title }}</h3>
              </div>
              <p class="relative text-lg leading-relaxed text-gray-700">
                {{ missionVision.mission.content }}
              </p>

              <!-- 装饰线条 -->
              <div class="relative mt-6 w-16 h-1 bg-gradient-to-r from-primary-400 to-blue-500 rounded-full"></div>
            </div>
          </div>

          <!-- 愿景 -->
          <div class="opacity-0 fade-in-up delay-400">
            <div class="bg-white rounded-2xl p-8 lg:p-10 shadow-lg border border-gray-100 h-full hover:shadow-xl transition-all duration-300 relative overflow-hidden group">
              <!-- 卡片装饰 -->
              <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform translate-x-10 -translate-y-10"></div>

              <div class="relative flex items-center mb-6">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center mr-4 group-hover:from-blue-200 group-hover:to-blue-300 transition-all duration-300">
                  <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900">{{ missionVision.vision.title }}</h3>
              </div>
              <p class="relative text-lg leading-relaxed text-gray-700">
                {{ missionVision.vision.content }}
              </p>

              <!-- 装饰线条 -->
              <div class="relative mt-6 w-16 h-1 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 核心价值观 -->
      <div class="mx-auto mt-24 max-w-6xl sm:mt-32 bg-white py-24 sm:py-32 relative overflow-hidden rounded-3xl">
        <!-- 背景装饰 -->
        <div class="absolute inset-0 overflow-hidden">
          <div class="absolute top-0 right-0 w-96 h-96 bg-primary-100 rounded-full opacity-20 transform translate-x-1/2 -translate-y-1/2"></div>
          <div class="absolute bottom-0 left-0 w-96 h-96 bg-blue-100 rounded-full opacity-20 transform -translate-x-1/2 translate-y-1/2"></div>
        </div>

        <div class="relative text-center mb-16 opacity-0 fade-in-up delay-500">
          <h2 class="text-4xl lg:text-5xl font-bold tracking-tight text-gray-900 mb-6">
            核心
            <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">价值观</span>
          </h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">指导我们前进的核心理念，塑造我们的企业文化和行为准则</p>
          <div class="w-24 h-1 bg-gradient-to-r from-primary-500 to-blue-500 rounded-full mx-auto mt-6"></div>
        </div>

        <div class="relative grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 px-8">
          <div v-for="(value, index) in coreValues" :key="value.name"
               class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 text-center group hover:shadow-xl transition-all duration-300 opacity-0 fade-in-up hover-float"
               :class="`delay-${600 + index * 100}`">
            <div class="mb-6">
              <div class="w-16 h-16 bg-gradient-to-br from-primary-100 to-blue-100 rounded-2xl flex items-center justify-center mx-auto group-hover:from-primary-200 group-hover:to-blue-200 transition-all duration-300">
                <component :is="value.icon" class="h-8 w-8 text-primary-600 group-hover:text-primary-700 transition-colors duration-300" aria-hidden="true" />
              </div>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4 group-hover:text-gray-800 transition-colors duration-300">
              {{ value.name }}
            </h3>
            <p class="text-gray-600 leading-relaxed text-sm group-hover:text-gray-700 transition-colors duration-300">
              {{ value.description }}
            </p>

            <!-- 装饰线条 -->
            <div class="mt-6 w-12 h-1 bg-gradient-to-r from-primary-400 to-blue-500 rounded-full mx-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
        </div>
      </div>

      <!-- 发展历程 -->
      <div class="mx-auto mt-24 max-w-5xl sm:mt-32">
        <div class="text-center mb-16 opacity-0 fade-in-up delay-1000">
          <h2 class="text-4xl lg:text-5xl font-bold tracking-tight text-gray-900 mb-6">
            发展
            <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">历程</span>
          </h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">见证我们的成长足迹，每一个里程碑都标志着我们在AI技术道路上的坚实步伐</p>
          <div class="w-24 h-1 bg-gradient-to-r from-primary-500 to-blue-500 rounded-full mx-auto mt-6"></div>
        </div>

        <div class="relative">
          <!-- 时间线背景 -->
          <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary-300 via-blue-400 to-purple-500"></div>

          <div class="space-y-8">
            <div v-for="(milestone, milestoneIdx) in companyMilestones" :key="milestone.year"
                 class="relative opacity-0 fade-in-up"
                 :class="`delay-${1100 + milestoneIdx * 100}`">
              <!-- 时间节点 -->
              <div class="absolute left-6 w-4 h-4 bg-white border-4 border-primary-500 rounded-full shadow-lg"></div>

              <!-- 内容卡片 -->
              <div class="ml-20">
                <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 group hover:shadow-xl transition-all duration-300 hover-float relative overflow-hidden">
                  <!-- 卡片装饰 -->
                  <div class="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-primary-50 to-blue-50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform translate-x-8 -translate-y-8"></div>

                  <div class="relative flex items-start space-x-4">
                    <div class="flex-shrink-0">
                      <div class="w-12 h-12 bg-gradient-to-br from-primary-100 to-blue-100 rounded-xl flex items-center justify-center group-hover:from-primary-200 group-hover:to-blue-200 transition-all duration-300">
                        <component :is="milestone.icon" class="h-6 w-6 text-primary-600 group-hover:text-primary-700 transition-colors duration-300" aria-hidden="true" />
                      </div>
                    </div>
                    <div class="flex-1 min-w-0">
                      <div class="flex items-center space-x-3 mb-3">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-primary-100 to-blue-100 text-primary-800">
                          {{ milestone.year }}
                        </span>
                      </div>
                      <p class="text-gray-700 leading-relaxed group-hover:text-gray-900 transition-colors duration-300">
                        {{ milestone.content }}
                      </p>
                    </div>
                  </div>

                  <!-- 装饰线条 -->
                  <div class="relative mt-4 w-16 h-1 bg-gradient-to-r from-primary-400 to-blue-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 行动号召 -->
      <div class="mx-auto mt-24 max-w-4xl sm:mt-32 mb-16">
        <div class="bg-gradient-to-br from-primary-600 via-blue-600 to-purple-700 rounded-3xl p-8 lg:p-12 text-center opacity-0 fade-in-up delay-1600 relative overflow-hidden">
          <!-- 背景装饰 -->
          <div class="absolute inset-0 overflow-hidden">
            <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full blur-xl transform translate-x-16 -translate-y-16"></div>
            <div class="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full blur-lg transform -translate-x-12 translate-y-12"></div>
          </div>

          <div class="relative">
            <h2 class="text-3xl lg:text-4xl font-bold text-white mb-6">
              {{ callToAction.title }}
            </h2>
            <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto leading-relaxed">
              {{ callToAction.description }}
            </p>

            <div class="flex flex-col sm:flex-row items-center justify-center gap-4">
              <template v-for="button in callToAction.buttons" :key="button.text">
                <router-link v-if="button.link.startsWith('/')" :to="button.link"
                             :class="[
                               'inline-flex items-center px-8 py-4 rounded-xl font-medium transition-all duration-300 group',
                               button.type === 'primary'
                                 ? 'bg-white text-primary-600 hover:bg-gray-100 hover:scale-105 shadow-lg hover:shadow-xl'
                                 : 'bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600 hover:scale-105'
                             ]">
                  {{ button.text }}
                  <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                  </svg>
                </router-link>
                <a v-else :href="button.link"
                   :class="[
                     'inline-flex items-center px-8 py-4 rounded-xl font-medium transition-all duration-300 group',
                     button.type === 'primary'
                       ? 'bg-white text-primary-600 hover:bg-gray-100 hover:scale-105 shadow-lg hover:shadow-xl'
                       : 'bg-transparent text-white border-2 border-white hover:bg-white hover:text-primary-600 hover:scale-105'
                   ]">
                  {{ button.text }}
                  <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                  </svg>
                </a>
              </template>
            </div>

            <!-- 装饰性元素 -->
            <div class="mt-8 flex justify-center">
              <div class="flex space-x-2">
                <div class="w-2 h-2 bg-white/40 rounded-full"></div>
                <div class="w-2 h-2 bg-white/60 rounded-full"></div>
                <div class="w-2 h-2 bg-white/80 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import PageHeader from '@/components/PageHeader.vue'
import {
  COMPANY_STORY,
  MISSION_VISION,
  CORE_VALUES,
  COMPANY_MILESTONES,
  CALL_TO_ACTION
} from '@/constants/company'

// 使用导入的常量
const companyStory = COMPANY_STORY
const missionVision = MISSION_VISION
const coreValues = CORE_VALUES
const companyMilestones = COMPANY_MILESTONES
const callToAction = CALL_TO_ACTION
</script>
