# 1Panel 自动化部署配置示例
# 复制此文件为 .env 并填入您的实际配置

# 1Panel 面板地址 (包含协议)
# 示例: https://panel.yourdomain.com
PANEL_URL=https://your-panel-domain.com

# 1Panel API 密钥
# 在 1Panel 管理面板的 设置 → API设置 中生成
PANEL_API_KEY=your-api-key-here

# 网站 ID
# 在 1Panel 网站管理页面的 URL 中可以找到
# 示例: 如果 URL 是 /websites/123，则 PANEL_SITE_ID=123
PANEL_SITE_ID=your-site-id

# 可选配置

# 部署环境 (production/staging)
DEPLOY_ENV=production

# 备份保留天数
BACKUP_RETENTION_DAYS=7

# 是否启用部署通知
ENABLE_NOTIFICATIONS=true

# 通知 Webhook URL (可选)
# NOTIFICATION_WEBHOOK=https://hooks.slack.com/services/xxx/xxx/xxx
