#!/bin/bash

# 本地部署脚本 - 用于测试1Panel API连接和部署流程
# 使用方法: ./deploy/local-deploy.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_success() {
    print_message $GREEN "✅ $1"
}

print_error() {
    print_message $RED "❌ $1"
}

print_warning() {
    print_message $YELLOW "⚠️ $1"
}

print_info() {
    print_message $BLUE "ℹ️ $1"
}

# 检查环境变量
check_env() {
    print_info "检查环境变量..."
    
    if [ -z "$PANEL_URL" ]; then
        print_error "PANEL_URL 环境变量未设置"
        echo "请设置: export PANEL_URL=https://your-panel-domain.com"
        exit 1
    fi
    
    if [ -z "$PANEL_API_KEY" ]; then
        print_error "PANEL_API_KEY 环境变量未设置"
        echo "请设置: export PANEL_API_KEY=your-api-key"
        exit 1
    fi
    
    if [ -z "$PANEL_SITE_ID" ]; then
        print_error "PANEL_SITE_ID 环境变量未设置"
        echo "请设置: export PANEL_SITE_ID=your-site-id"
        exit 1
    fi
    
    print_success "环境变量检查完成"
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm 未安装"
        exit 1
    fi
    
    if ! command -v curl &> /dev/null; then
        print_error "curl 未安装"
        exit 1
    fi
    
    print_success "依赖检查完成"
}

# 测试1Panel API连接
test_api_connection() {
    print_info "测试1Panel API连接..."
    
    response=$(curl -s -w "%{http_code}" -o /tmp/auth_response.json \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $PANEL_API_KEY" \
        "$PANEL_URL/api/v1/auth/check")
    
    if [ "$response" != "200" ]; then
        print_error "1Panel API连接失败，HTTP状态码: $response"
        if [ -f /tmp/auth_response.json ]; then
            echo "响应内容:"
            cat /tmp/auth_response.json
        fi
        exit 1
    fi
    
    print_success "1Panel API连接成功"
}

# 获取网站信息
get_site_info() {
    print_info "获取网站信息..."
    
    response=$(curl -s -w "%{http_code}" -o /tmp/site_response.json \
        -H "Authorization: Bearer $PANEL_API_KEY" \
        "$PANEL_URL/api/v1/websites/$PANEL_SITE_ID")
    
    if [ "$response" != "200" ]; then
        print_error "获取网站信息失败，HTTP状态码: $response"
        if [ -f /tmp/site_response.json ]; then
            echo "响应内容:"
            cat /tmp/site_response.json
        fi
        exit 1
    fi
    
    # 提取网站信息
    SITE_PATH=$(cat /tmp/site_response.json | grep -o '"path":"[^"]*"' | cut -d'"' -f4)
    SITE_DOMAIN=$(cat /tmp/site_response.json | grep -o '"primaryDomain":"[^"]*"' | cut -d'"' -f4)
    
    print_success "网站信息获取成功"
    echo "  网站路径: $SITE_PATH"
    echo "  主域名: $SITE_DOMAIN"
}

# 构建项目
build_project() {
    print_info "构建项目..."
    
    # 检查是否在项目根目录
    if [ ! -f "package.json" ]; then
        print_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 安装依赖
    print_info "安装依赖..."
    npm ci
    
    # 构建项目
    print_info "构建项目..."
    npm run build
    
    # 检查构建结果
    if [ ! -d "dist" ]; then
        print_error "构建失败，dist目录不存在"
        exit 1
    fi
    
    print_success "项目构建完成"
}

# 创建部署包
create_deployment_package() {
    print_info "创建部署包..."
    
    cd dist
    tar -czf ../website.tar.gz .
    cd ..
    
    if [ ! -f "website.tar.gz" ]; then
        print_error "部署包创建失败"
        exit 1
    fi
    
    local size=$(du -h website.tar.gz | cut -f1)
    print_success "部署包创建完成 (大小: $size)"
}

# 上传文件到1Panel
upload_to_panel() {
    print_info "上传部署包到1Panel..."
    
    response=$(curl -s -w "%{http_code}" -o /tmp/upload_response.json \
        -H "Authorization: Bearer $PANEL_API_KEY" \
        -F "file=@website.tar.gz" \
        -F "path=/tmp" \
        "$PANEL_URL/api/v1/files/upload")
    
    if [ "$response" != "200" ]; then
        print_error "文件上传失败，HTTP状态码: $response"
        if [ -f /tmp/upload_response.json ]; then
            echo "响应内容:"
            cat /tmp/upload_response.json
        fi
        exit 1
    fi
    
    print_success "文件上传成功"
}

# 备份当前网站
backup_current_site() {
    print_info "备份当前网站..."
    
    local backup_name="backup_$(date +%Y%m%d_%H%M%S)"
    
    response=$(curl -s -w "%{http_code}" -o /tmp/backup_response.json \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $PANEL_API_KEY" \
        -d "{\"path\":\"$SITE_PATH\",\"name\":\"$backup_name\"}" \
        "$PANEL_URL/api/v1/files/compress")
    
    if [ "$response" != "200" ]; then
        print_warning "备份失败，但继续部署，HTTP状态码: $response"
        if [ -f /tmp/backup_response.json ]; then
            echo "响应内容:"
            cat /tmp/backup_response.json
        fi
    else
        print_success "网站备份成功: $backup_name"
    fi
}

# 部署新版本
deploy_new_version() {
    print_info "部署新版本..."
    
    # 清空网站目录
    print_info "清空网站目录..."
    response=$(curl -s -w "%{http_code}" -o /tmp/clear_response.json \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $PANEL_API_KEY" \
        -X DELETE \
        "$PANEL_URL/api/v1/files?path=$SITE_PATH/*")
    
    if [ "$response" != "200" ]; then
        print_warning "清空目录失败，但继续部署，HTTP状态码: $response"
    else
        print_success "网站目录已清空"
    fi
    
    # 解压新文件到网站目录
    print_info "解压新文件到网站目录..."
    response=$(curl -s -w "%{http_code}" -o /tmp/extract_response.json \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $PANEL_API_KEY" \
        -d "{\"source\":\"/tmp/website.tar.gz\",\"target\":\"$SITE_PATH\"}" \
        "$PANEL_URL/api/v1/files/decompress")
    
    if [ "$response" != "200" ]; then
        print_error "文件解压失败，HTTP状态码: $response"
        if [ -f /tmp/extract_response.json ]; then
            echo "响应内容:"
            cat /tmp/extract_response.json
        fi
        exit 1
    fi
    
    print_success "文件解压成功"
    
    # 设置文件权限
    print_info "设置文件权限..."
    response=$(curl -s -w "%{http_code}" -o /tmp/chmod_response.json \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $PANEL_API_KEY" \
        -d "{\"path\":\"$SITE_PATH\",\"mode\":\"755\",\"recursive\":true}" \
        "$PANEL_URL/api/v1/files/mode")
    
    if [ "$response" != "200" ]; then
        print_warning "设置权限失败，但部署可能成功，HTTP状态码: $response"
    else
        print_success "文件权限设置成功"
    fi
}

# 清理临时文件
cleanup() {
    print_info "清理临时文件..."
    
    # 清理本地文件
    if [ -f "website.tar.gz" ]; then
        rm website.tar.gz
        print_success "本地临时文件清理完成"
    fi
    
    # 清理1Panel临时文件
    response=$(curl -s -w "%{http_code}" -o /tmp/cleanup_response.json \
        -H "Authorization: Bearer $PANEL_API_KEY" \
        -X DELETE \
        "$PANEL_URL/api/v1/files?path=/tmp/website.tar.gz")
    
    if [ "$response" != "200" ]; then
        print_warning "清理1Panel临时文件失败，HTTP状态码: $response"
    else
        print_success "1Panel临时文件清理完成"
    fi
    
    # 清理本地临时响应文件
    rm -f /tmp/*_response.json
}

# 主函数
main() {
    echo "🚀 开始本地部署流程..."
    echo "================================"
    
    check_env
    check_dependencies
    test_api_connection
    get_site_info
    build_project
    create_deployment_package
    upload_to_panel
    backup_current_site
    deploy_new_version
    cleanup
    
    echo "================================"
    print_success "部署完成！"
    echo ""
    echo "📅 部署时间: $(date)"
    echo "🌐 网站地址: http://$SITE_DOMAIN"
    echo ""
    echo "请访问您的网站验证部署结果。"
}

# 错误处理
trap 'print_error "部署过程中发生错误，正在清理..."; cleanup; exit 1' ERR

# 运行主函数
main
