# 🚀 自动化部署配置指南

本文档详细说明如何配置 GitHub Actions + 1Panel API 自动化部署。

## 📋 配置清单

### 1. 1Panel 配置

#### 1.1 启用API功能
1. 登录1Panel管理面板
2. 进入 `设置` → `安全` → `API设置`
3. 启用API功能
4. 生成API密钥并妥善保存

#### 1.2 获取网站ID
1. 进入 `网站` 管理页面
2. 找到您的网站，点击 `设置`
3. 在URL中可以看到网站ID，格式如：`/websites/123`
4. 记录网站ID（如：123）

#### 1.3 确认网站路径
1. 在网站设置中查看 `网站目录`
2. 记录完整路径（如：`/opt/1panel/apps/openresty/openresty/www/sites/your-site.com/index`）

### 2. GitHub 配置

#### 2.1 添加Secrets
在GitHub仓库中添加以下Secrets：

| Secret名称 | 说明 | 示例值 |
|-----------|------|--------|
| `PANEL_URL` | 1Panel面板地址 | `https://panel.yourdomain.com` |
| `PANEL_API_KEY` | 1Panel API密钥 | `your-api-key-here` |
| `PANEL_SITE_ID` | 网站ID | `123` |

#### 2.2 配置步骤
1. 进入GitHub仓库
2. 点击 `Settings` → `Secrets and variables` → `Actions`
3. 点击 `New repository secret`
4. 依次添加上述三个secrets

### 3. 部署流程说明

#### 3.1 自动触发
- 推送代码到 `main` 分支时自动触发部署
- 支持通过GitHub界面手动触发部署

#### 3.2 部署步骤
1. **代码检出**: 获取最新代码
2. **环境准备**: 设置Node.js环境
3. **依赖安装**: 安装项目依赖
4. **类型检查**: 运行TypeScript检查
5. **项目构建**: 构建生产版本
6. **创建部署包**: 打包dist目录
7. **上传到1Panel**: 通过API上传文件
8. **备份当前网站**: 自动备份现有文件
9. **部署新版本**: 解压并替换网站文件
10. **设置权限**: 配置正确的文件权限
11. **清理临时文件**: 删除临时文件

#### 3.3 安全特性
- **自动备份**: 每次部署前自动备份当前网站
- **权限验证**: 验证API密钥和权限
- **错误处理**: 详细的错误日志和回滚机制
- **状态反馈**: 实时部署状态和结果通知

## 🔧 故障排除

### 常见问题

#### 1. API认证失败
**错误信息**: `1Panel API认证失败`

**解决方案**:
- 检查 `PANEL_URL` 是否正确（包含协议，如 https://）
- 验证 `PANEL_API_KEY` 是否有效
- 确认1Panel API功能已启用

#### 2. 网站ID错误
**错误信息**: `获取网站信息失败`

**解决方案**:
- 检查 `PANEL_SITE_ID` 是否正确
- 确认API密钥有访问该网站的权限

#### 3. 文件上传失败
**错误信息**: `文件上传失败`

**解决方案**:
- 检查1Panel存储空间是否充足
- 确认网络连接正常
- 验证文件大小是否超过限制

#### 4. 权限问题
**错误信息**: `设置权限失败`

**解决方案**:
- 确认1Panel用户有足够权限
- 检查网站目录权限设置

### 调试方法

#### 1. 查看部署日志
1. 进入GitHub仓库
2. 点击 `Actions` 标签
3. 选择最近的工作流运行
4. 查看详细日志

#### 2. 手动测试API
```bash
# 测试API连接
curl -H "Authorization: Bearer YOUR_API_KEY" \
     "https://your-panel-domain.com/api/v1/auth/check"

# 获取网站信息
curl -H "Authorization: Bearer YOUR_API_KEY" \
     "https://your-panel-domain.com/api/v1/websites/YOUR_SITE_ID"
```

#### 3. 本地测试构建
```bash
# 本地测试构建过程
npm install
npm run build
cd dist && tar -czf ../test.tar.gz . && cd ..
```

## 📈 性能优化

### 1. 构建优化
- 启用代码分割
- 压缩静态资源
- 优化图片格式

### 2. 部署优化
- 增量部署（仅更新变更文件）
- 并行处理
- 缓存依赖

### 3. 监控建议
- 设置部署通知
- 监控网站可用性
- 记录部署历史

## 🔒 安全建议

1. **API密钥管理**
   - 定期轮换API密钥
   - 限制API权限范围
   - 使用强密码策略

2. **访问控制**
   - 限制GitHub仓库访问权限
   - 使用分支保护规则
   - 启用双因素认证

3. **备份策略**
   - 定期备份网站数据
   - 测试备份恢复流程
   - 保留多个备份版本

## 📞 技术支持

如果遇到问题，请：
1. 查看GitHub Actions日志
2. 检查1Panel系统日志
3. 参考本文档的故障排除部分
4. 联系技术支持团队

---

**注意**: 请确保在生产环境中测试部署流程，并做好数据备份。
