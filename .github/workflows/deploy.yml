name: 自动化部署到1Panel

on:
  # 推送到main分支时自动触发
  push:
    branches: [ main ]
  
  # 支持手动触发
  workflow_dispatch:
    inputs:
      environment:
        description: '部署环境'
        required: true
        default: 'production'
        type: choice
        options:
        - production
        - staging

# 设置权限
permissions:
  contents: read

jobs:
  deploy:
    name: 构建并部署
    runs-on: ubuntu-latest
    
    steps:
    # 1. 检出代码
    - name: 📥 检出代码
      uses: actions/checkout@v4
      
    # 2. 设置Node.js环境
    - name: 🔧 设置Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    # 3. 安装依赖
    - name: 📦 安装依赖
      run: npm ci
      
    # 4. 运行类型检查
    - name: 🔍 TypeScript类型检查
      run: npm run build
      
    # 5. 构建项目
    - name: 🏗️ 构建项目
      run: npm run build
      
    # 6. 创建部署包
    - name: 📦 创建部署包
      run: |
        cd dist
        tar -czf ../website.tar.gz .
        cd ..
        ls -la website.tar.gz
        
    # 7. 部署到1Panel
    - name: 🚀 部署到1Panel
      env:
        PANEL_URL: ${{ secrets.PANEL_URL }}
        PANEL_API_KEY: ${{ secrets.PANEL_API_KEY }}
        PANEL_SITE_ID: ${{ secrets.PANEL_SITE_ID }}
      run: |
        # 创建部署脚本
        cat > deploy.sh << 'EOF'
        #!/bin/bash
        set -e
        
        echo "🔐 验证1Panel连接..."
        
        # 验证API连接
        response=$(curl -s -w "%{http_code}" -o /tmp/auth_response.json \
          -H "Content-Type: application/json" \
          -H "Authorization: Bearer $PANEL_API_KEY" \
          "$PANEL_URL/api/v1/auth/check")
        
        if [ "$response" != "200" ]; then
          echo "❌ 1Panel API认证失败，HTTP状态码: $response"
          cat /tmp/auth_response.json
          exit 1
        fi
        
        echo "✅ 1Panel API连接成功"
        
        # 上传文件到1Panel
        echo "📤 上传部署包到1Panel..."
        
        upload_response=$(curl -s -w "%{http_code}" -o /tmp/upload_response.json \
          -H "Authorization: Bearer $PANEL_API_KEY" \
          -F "file=@website.tar.gz" \
          -F "path=/tmp" \
          "$PANEL_URL/api/v1/files/upload")
        
        if [ "$upload_response" != "200" ]; then
          echo "❌ 文件上传失败，HTTP状态码: $upload_response"
          cat /tmp/upload_response.json
          exit 1
        fi
        
        echo "✅ 文件上传成功"
        
        # 获取网站信息
        echo "🔍 获取网站信息..."
        
        site_response=$(curl -s -w "%{http_code}" -o /tmp/site_response.json \
          -H "Authorization: Bearer $PANEL_API_KEY" \
          "$PANEL_URL/api/v1/websites/$PANEL_SITE_ID")
        
        if [ "$site_response" != "200" ]; then
          echo "❌ 获取网站信息失败，HTTP状态码: $site_response"
          cat /tmp/site_response.json
          exit 1
        fi
        
        # 提取网站路径
        SITE_PATH=$(cat /tmp/site_response.json | grep -o '"path":"[^"]*"' | cut -d'"' -f4)
        echo "📁 网站路径: $SITE_PATH"
        
        # 备份当前网站
        echo "💾 备份当前网站..."
        
        backup_response=$(curl -s -w "%{http_code}" -o /tmp/backup_response.json \
          -H "Content-Type: application/json" \
          -H "Authorization: Bearer $PANEL_API_KEY" \
          -d "{\"path\":\"$SITE_PATH\",\"name\":\"backup_$(date +%Y%m%d_%H%M%S)\"}" \
          "$PANEL_URL/api/v1/files/compress")
        
        if [ "$backup_response" != "200" ]; then
          echo "⚠️ 备份失败，但继续部署，HTTP状态码: $backup_response"
          cat /tmp/backup_response.json
        else
          echo "✅ 网站备份成功"
        fi
        
        # 清空网站目录
        echo "🧹 清空网站目录..."
        
        clear_response=$(curl -s -w "%{http_code}" -o /tmp/clear_response.json \
          -H "Content-Type: application/json" \
          -H "Authorization: Bearer $PANEL_API_KEY" \
          -X DELETE \
          "$PANEL_URL/api/v1/files?path=$SITE_PATH/*")
        
        if [ "$clear_response" != "200" ]; then
          echo "⚠️ 清空目录失败，但继续部署，HTTP状态码: $clear_response"
          cat /tmp/clear_response.json
        else
          echo "✅ 网站目录已清空"
        fi
        
        # 解压新文件到网站目录
        echo "📂 解压新文件到网站目录..."
        
        extract_response=$(curl -s -w "%{http_code}" -o /tmp/extract_response.json \
          -H "Content-Type: application/json" \
          -H "Authorization: Bearer $PANEL_API_KEY" \
          -d "{\"source\":\"/tmp/website.tar.gz\",\"target\":\"$SITE_PATH\"}" \
          "$PANEL_URL/api/v1/files/decompress")
        
        if [ "$extract_response" != "200" ]; then
          echo "❌ 文件解压失败，HTTP状态码: $extract_response"
          cat /tmp/extract_response.json
          exit 1
        fi
        
        echo "✅ 文件解压成功"
        
        # 设置文件权限
        echo "🔐 设置文件权限..."
        
        chmod_response=$(curl -s -w "%{http_code}" -o /tmp/chmod_response.json \
          -H "Content-Type: application/json" \
          -H "Authorization: Bearer $PANEL_API_KEY" \
          -d "{\"path\":\"$SITE_PATH\",\"mode\":\"755\",\"recursive\":true}" \
          "$PANEL_URL/api/v1/files/mode")
        
        if [ "$chmod_response" != "200" ]; then
          echo "⚠️ 设置权限失败，但部署可能成功，HTTP状态码: $chmod_response"
          cat /tmp/chmod_response.json
        else
          echo "✅ 文件权限设置成功"
        fi
        
        # 清理临时文件
        echo "🧹 清理临时文件..."
        
        cleanup_response=$(curl -s -w "%{http_code}" -o /tmp/cleanup_response.json \
          -H "Authorization: Bearer $PANEL_API_KEY" \
          -X DELETE \
          "$PANEL_URL/api/v1/files?path=/tmp/website.tar.gz")
        
        if [ "$cleanup_response" != "200" ]; then
          echo "⚠️ 清理临时文件失败，HTTP状态码: $cleanup_response"
        else
          echo "✅ 临时文件清理成功"
        fi
        
        echo ""
        echo "🎉 部署完成！"
        echo "📅 部署时间: $(date)"
        echo "🌐 网站地址: 请检查您的域名"
        echo ""
        
        EOF
        
        # 执行部署脚本
        chmod +x deploy.sh
        ./deploy.sh
        
    # 8. 部署状态通知
    - name: 📢 部署状态通知
      if: always()
      run: |
        if [ "${{ job.status }}" == "success" ]; then
          echo "✅ 部署成功完成！"
          echo "🌐 网站已更新到最新版本"
          echo "📅 部署时间: $(date)"
        else
          echo "❌ 部署失败！"
          echo "请检查日志并联系管理员"
        fi
